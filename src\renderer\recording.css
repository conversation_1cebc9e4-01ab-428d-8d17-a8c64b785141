* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: rgba(0, 0, 0, 0.85);
    color: white;
    border-radius: 12px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    zoom: 1;  /* 確保縮放比例正確 */
    transform: scale(1);  /* 防止意外縮放 */
}

.container {
    padding: 25px;
    text-align: center;
    height: 250px;  /* 增加高度 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.recording-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    transition: all 0.3s ease;
}

.recording-icon.recording {
    background: linear-gradient(45deg, #ff4757, #ff6b7a);
    animation: pulse 1.5s infinite;
}

.recording-icon.processing {
    background: linear-gradient(45deg, #3742fa, #5352ed);
    animation: spin 1s linear infinite;
}

.recording-icon.completed {
    background: linear-gradient(45deg, #2ed573, #7bed9f);
}

.recording-icon.error {
    background: linear-gradient(45deg, #ff4757, #ff3838);
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.mode-text {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #ffffff;
}

.status-text {
    font-size: 14px;
    color: #cccccc;
    margin-bottom: 8px;
}

.duration {
    font-size: 12px;
    color: #999999;
    font-family: 'Courier New', monospace;
}

.message {
    font-size: 12px;
    color: #ffcc00;
    margin-top: 8px;
    max-width: 350px;
    word-wrap: break-word;
}

.volume-container {
    width: 100%;
    max-width: 300px;
    margin: 10px 0;
}

.volume-label {
    font-size: 11px;
    color: #999999;
    margin-bottom: 5px;
}

.volume-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.volume-fill {
    height: 100%;
    background: linear-gradient(90deg, #2ed573, #fffa65, #ff4757);
    border-radius: 4px;
    transition: width 0.1s ease;
    width: 0%;
}

.volume-text {
    font-size: 10px;
    color: #cccccc;
    margin-top: 3px;
    text-align: center;
}

.close-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 20px;
    height: 20px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 50%;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TextInputService = void 0;
const child_process_1 = require("child_process");
const os_1 = require("os");
class TextInputService {
    constructor() {
        this.lastInputText = '';
    }
    async inputText(text) {
        try {
            console.log('⌨️ Inputting text:', text.substring(0, 50) + '...');
            const cleanText = text.replace(/[""]/g, '"').replace(/\r?\n/g, ' ');
            return new Promise((resolve, reject) => {
                let process;
                if ((0, os_1.platform)() === 'darwin') {
                    // macOS - 使用 osascript
                    const script = `tell application "System Events" to keystroke "${cleanText.replace(/"/g, '\\"')}"`;
                    process = (0, child_process_1.spawn)('osascript', ['-e', script]);
                }
                else {
                    // Windows - 使用 PowerShell
                    const script = `
            Add-Type -AssemblyName System.Windows.Forms
            Start-Sleep -Milliseconds 200
            [System.Windows.Forms.SendKeys]::SendWait("${cleanText.replace(/"/g, '""')}")
          `;
                    process = (0, child_process_1.spawn)('powershell', ['-Command', script]);
                }
                process.on('close', (code) => {
                    if (code === 0) {
                        console.log('✅ Text input completed successfully');
                        resolve(undefined);
                    }
                    else {
                        console.error(`❌ Text input process exited with code: ${code}`);
                        reject(new Error(`Text input failed with code: ${code}`));
                    }
                });
                process.on('error', (error) => {
                    console.error('❌ Text input process error:', error);
                    reject(error);
                });
            });
        }
        catch (error) {
            console.error('❌ Text input failed:', error);
            throw error;
        }
    }
    async inputTextStreaming(text) {
        try {
            // 只輸入新增的文字部分
            if (text.length <= this.lastInputText.length) {
                return; // 沒有新內容
            }
            const newText = text.substring(this.lastInputText.length);
            // 如果新文字太短，跳過（避免頻繁輸入單個字符）
            if (newText.trim().length < 2) {
                return;
            }
            console.log('⌨️ Streaming input:', newText.substring(0, 30) + '...');
            const cleanText = newText.replace(/[""]/g, '"').replace(/\r?\n/g, ' ');
            if ((0, os_1.platform)() === 'darwin') {
                // macOS - 使用 osascript
                const script = `tell application "System Events" to keystroke "${cleanText.replace(/"/g, '\\"')}"`;
                const process = (0, child_process_1.spawn)('osascript', ['-e', script]);
                await new Promise((resolve) => {
                    process.on('close', resolve);
                });
            }
            else {
                // Windows - 使用 PowerShell，進一步減少延遲
                const script = `
          Add-Type -AssemblyName System.Windows.Forms
          Start-Sleep -Milliseconds 20
          [System.Windows.Forms.SendKeys]::SendWait("${cleanText.replace(/"/g, '""')}")
        `;
                const process = (0, child_process_1.spawn)('powershell', ['-Command', script]);
                await new Promise((resolve) => {
                    process.on('close', resolve);
                });
            }
            this.lastInputText = text;
            console.log('✅ Streaming text input completed');
        }
        catch (error) {
            console.error('❌ Streaming text input failed:', error);
            throw error;
        }
    }
    resetStreamingState() {
        this.lastInputText = '';
    }
}
exports.TextInputService = TextInputService;

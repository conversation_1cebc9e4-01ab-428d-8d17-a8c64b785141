var s=Object.defineProperty;var c=(i,e,t)=>e in i?s(i,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[e]=t;var n=(i,e,t)=>c(i,typeof e!="symbol"?e+"":e,t);import{D as d}from"./index-D_t2e9pP.js";class a{async inputText(e){console.log("Mock: Inputting text:",e);try{if(navigator.clipboard&&navigator.clipboard.writeText)await navigator.clipboard.writeText(e),console.log("Text copied to clipboard:",e),this.showNotification(`已複製到剪貼簿: ${e.substring(0,50)}${e.length>50?"...":""}`);else{const t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.select(),document.execCommand("copy"),document.body.removeChild(t),this.showNotification(`已複製到剪貼簿: ${e.substring(0,50)}${e.length>50?"...":""}`)}}catch(t){console.error("Failed to copy to clipboard:",t),this.showNotification("複製到剪貼簿失敗，請手動複製文字")}}showNotification(e){const t=document.createElement("div");t.style.cssText=`
      position: fixed;
      top: 20px;
      right: 20px;
      background: #28a745;
      color: white;
      padding: 12px 16px;
      border-radius: 6px;
      font-size: 14px;
      z-index: 10000;
      max-width: 300px;
      word-wrap: break-word;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    `,t.textContent=e,document.body.appendChild(t),setTimeout(()=>{t.parentNode&&t.parentNode.removeChild(t)},3e3)}async testTextInput(){try{return await this.inputText("SpeechPilot 測試文字輸入"),!0}catch(e){return console.error("Text input test failed:",e),!1}}async getClipboardContent(){try{return navigator.clipboard&&navigator.clipboard.readText?await navigator.clipboard.readText():""}catch(e){return console.error("Failed to read clipboard:",e),""}}async setClipboardContent(e){await this.inputText(e)}cleanup(){}}const l=new a;class g{constructor(){n(this,"config",d);n(this,"listeners",new Map);n(this,"lastRecordingMode","ai");n(this,"platform","web");n(this,"version","1.0.0-mock")}async getConfig(){return{...this.config}}async updateConfig(e){this.config={...this.config,...e},this.emit("config-updated",this.config)}async startRecording(e){console.log(`🎙️ MockElectronAPI: Starting recording in ${e} mode`),this.lastRecordingMode=e;const t={isRecording:!0,mode:e,duration:0,status:"recording"};console.log("🎙️ MockElectronAPI: Emitting recording-state-changed:",t),this.emit("recording-state-changed",t),console.log("🎙️ MockElectronAPI: Emitting show-recording-window"),this.emit("show-recording-window")}async stopRecording(){console.log("🛑 MockElectronAPI: Stopping recording");const e={isRecording:!1,mode:this.lastRecordingMode||"direct",duration:5,status:"processing"};console.log("🛑 MockElectronAPI: Emitting processing state:",e),this.emit("recording-state-changed",e),setTimeout(async()=>{console.log("⚙️ MockElectronAPI: Starting processing simulation");let t;this.lastRecordingMode==="direct"?(t="這是模擬的直接語音轉錄結果，使用 gpt-4o-mini-transcribe 模型進行高精度轉錄。",console.log("📝 MockElectronAPI: Using direct transcription mode")):(t="這是模擬的 AI 智能語音處理結果。SpeechPilot 正在正常工作！使用 gpt-4o-mini-audio-preview 模型進行智能理解。",console.log("🤖 MockElectronAPI: Using AI intelligent mode")),console.log("📋 MockElectronAPI: Generated result:",t),console.log("⌨️ MockElectronAPI: Inputting text to clipboard"),await l.inputText(t);const o={isRecording:!1,mode:this.lastRecordingMode||"direct",duration:5,status:"completed",result:t};console.log("✅ MockElectronAPI: Emitting completed state:",o),this.emit("recording-state-changed",o),console.log("📤 MockElectronAPI: Emitting processing result"),this.emit("processing-result",{success:!0,text:t,processingTime:Date.now()}),setTimeout(()=>{console.log("🚪 MockElectronAPI: Hiding recording window"),this.emit("hide-recording-window")},3e3)},2e3)}async getAudioDevices(){return[{deviceId:"default",label:"預設麥克風",kind:"audioinput"},{deviceId:"mock-device-1",label:"模擬麥克風 1",kind:"audioinput"}]}async showSettings(){console.log("Mock: Show settings")}async hideSettings(){console.log("Mock: Hide settings")}async showRecordingWindow(){console.log("Mock: Show recording window")}async hideRecordingWindow(){console.log("Mock: Hide recording window")}async minimizeToTray(){console.log("Mock: Minimize to tray")}async quitApp(){console.log("Mock: Quit app")}onConfigUpdated(e){return this.addEventListener("config-updated",e)}onRecordingStateChanged(e){return this.addEventListener("recording-state-changed",e)}onHotkeyTriggered(e){return this.addEventListener("hotkey-triggered",e)}onProcessingResult(e){return this.addEventListener("processing-result",e)}onErrorOccurred(e){return this.addEventListener("error-occurred",e)}addEventListener(e,t){return this.listeners.has(e)||this.listeners.set(e,[]),this.listeners.get(e).push(t),()=>{const o=this.listeners.get(e);if(o){const r=o.indexOf(t);r>-1&&o.splice(r,1)}}}emit(e,t){const o=this.listeners.get(e);o&&o.forEach(r=>r(t))}simulateHotkeyTrigger(e){this.emit("hotkey-triggered",{mode:e})}simulateError(e){this.emit("error-occurred",{message:e})}}const p=new g;typeof window<"u"&&!window.electronAPI&&(window.electronAPI=p);export{g as MockElectronAPI,p as mockElectronAPI};

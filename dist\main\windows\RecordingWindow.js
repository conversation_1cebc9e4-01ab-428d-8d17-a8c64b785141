"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecordingWindow = void 0;
const electron_1 = require("electron");
const path_1 = require("path");
class RecordingWindow {
    constructor() {
        this.window = null;
        this.status = {
            isRecording: false,
            mode: 'ai',
            duration: 0,
            status: 'recording'
        };
    }
    create() {
        console.log('🪟 Creating recording window...');
        if (this.window) {
            console.log('🪟 Window already exists, focusing...');
            this.window.focus();
            return;
        }
        // 獲取主顯示器的工作區域
        const primaryDisplay = electron_1.screen.getPrimaryDisplay();
        const { width, height } = primaryDisplay.workAreaSize;
        this.window = new electron_1.BrowserWindow({
            width: 400,
            height: 250, // 增加高度從 200 到 250
            x: Math.round((width - 400) / 2),
            y: Math.round((height - 250) / 2),
            resizable: false,
            minimizable: false,
            maximizable: false,
            alwaysOnTop: true,
            skipTaskbar: true,
            frame: false,
            transparent: true,
            show: false, // 不自動顯示，避免搶奪焦點
            focusable: false, // 不可獲得焦點
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: false,
                zoomFactor: 1.0 // 確保縮放比例為 100%
            }
        });
        // 載入獨立的 HTML 文件 - 指向源文件
        const htmlPath = (0, path_1.join)(process.cwd(), 'src/renderer/recording.html');
        console.log('🪟 Loading HTML file:', htmlPath);
        this.window.loadFile(htmlPath)
            .then(() => {
            console.log('🪟 HTML loaded successfully');
            // 顯示窗口但不搶奪焦點
            this.window?.showInactive();
        })
            .catch((error) => {
            console.error('❌ Failed to load HTML:', error);
        });
        // 窗口關閉事件
        this.window.on('closed', () => {
            // 通知主進程停止錄音
            const { ipcMain } = require('electron');
            ipcMain.emit('recording-window-closed');
            this.window = null;
        });
        // 發送初始狀態
        this.window.webContents.once('dom-ready', () => {
            this.updateStatus(this.status);
        });
        console.log('🪟 Recording window created');
    }
    updateStatus(status) {
        this.status = { ...this.status, ...status };
        if (this.window && !this.window.isDestroyed()) {
            this.window.webContents.send('status-update', this.status);
        }
    }
    close(delay = 1000) {
        if (this.window && !this.window.isDestroyed()) {
            setTimeout(() => {
                if (this.window && !this.window.isDestroyed()) {
                    this.window.close();
                }
            }, delay);
        }
    }
    isVisible() {
        return this.window !== null && !this.window.isDestroyed();
    }
    focus() {
        if (this.window && !this.window.isDestroyed()) {
            this.window.focus();
        }
    }
}
exports.RecordingWindow = RecordingWindow;

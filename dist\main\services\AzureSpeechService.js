"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AzureSpeechService = void 0;
const sdk = __importStar(require("microsoft-cognitiveservices-speech-sdk"));
const events_1 = require("events");
const constants_1 = require("../../shared/constants");
class AzureSpeechService extends events_1.EventEmitter {
    constructor() {
        super();
        Object.defineProperty(this, "speechConfig", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: null
        });
        Object.defineProperty(this, "config", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: null
        });
    }
    updateConfig(config) {
        this.config = config;
        this.initializeSpeechConfig();
    }
    initializeSpeechConfig() {
        if (!this.config?.apiKey || !this.config?.region) {
            console.warn('Azure Speech Service config is incomplete');
            return;
        }
        try {
            this.speechConfig = sdk.SpeechConfig.fromSubscription(this.config.apiKey, this.config.region);
            // 設置語音識別語言
            this.speechConfig.speechRecognitionLanguage = 'zh-TW';
            // 設置輸出格式
            this.speechConfig.outputFormat = sdk.OutputFormat.Detailed;
            // 啟用詳細結果
            this.speechConfig.requestWordLevelTimestamps();
            console.log('Azure Speech Service initialized');
        }
        catch (error) {
            console.error('Failed to initialize Azure Speech Service:', error);
            this.speechConfig = null;
        }
    }
    async speechToText(audioBuffer) {
        if (!this.speechConfig) {
            throw new Error(constants_1.ERROR_MESSAGES.API_KEY_MISSING);
        }
        return new Promise((resolve, reject) => {
            try {
                // 創建音頻配置
                const audioConfig = this.createAudioConfigFromBuffer(audioBuffer);
                // 創建語音識別器
                const recognizer = new sdk.SpeechRecognizer(this.speechConfig, audioConfig);
                let recognizedText = '';
                let hasResult = false;
                // 設置事件處理器
                recognizer.recognizing = (_, e) => {
                    console.log(`Recognizing: ${e.result.text}`);
                };
                recognizer.recognized = (_, e) => {
                    if (e.result.reason === sdk.ResultReason.RecognizedSpeech) {
                        recognizedText += e.result.text;
                        hasResult = true;
                        console.log(`Recognized: ${e.result.text}`);
                    }
                    else if (e.result.reason === sdk.ResultReason.NoMatch) {
                        console.log('No speech could be recognized');
                    }
                };
                recognizer.canceled = (_, e) => {
                    console.log(`Recognition canceled: ${e.reason}`);
                    if (e.reason === sdk.CancellationReason.Error) {
                        const errorMessage = `Error: ${e.errorCode} - ${e.errorDetails}`;
                        console.error(errorMessage);
                        recognizer.close();
                        reject(new Error(errorMessage));
                    }
                    else {
                        recognizer.close();
                        if (hasResult) {
                            resolve(recognizedText.trim());
                        }
                        else {
                            reject(new Error('No speech recognized'));
                        }
                    }
                };
                recognizer.sessionStopped = (_, _e) => {
                    console.log('Session stopped');
                    recognizer.close();
                    if (hasResult) {
                        resolve(recognizedText.trim());
                    }
                    else {
                        reject(new Error('No speech recognized'));
                    }
                };
                // 開始識別
                recognizer.recognizeOnceAsync((result) => {
                    recognizer.close();
                    if (result.reason === sdk.ResultReason.RecognizedSpeech) {
                        resolve(result.text);
                    }
                    else if (result.reason === sdk.ResultReason.NoMatch) {
                        reject(new Error('No speech could be recognized'));
                    }
                    else {
                        reject(new Error(`Recognition failed: ${result.reason}`));
                    }
                }, (error) => {
                    recognizer.close();
                    reject(new Error(`Recognition error: ${error}`));
                });
                // 設置超時
                setTimeout(() => {
                    recognizer.close();
                    if (!hasResult) {
                        reject(new Error('Recognition timeout'));
                    }
                }, constants_1.API_CONFIG.timeout);
            }
            catch (error) {
                console.error('Speech recognition error:', error);
                reject(error);
            }
        });
    }
    createAudioConfigFromBuffer(audioBuffer) {
        try {
            // 創建音頻流
            const audioStream = sdk.AudioInputStream.createPushStream();
            // 寫入音頻數據
            audioStream.write(audioBuffer);
            audioStream.close();
            // 創建音頻配置
            return sdk.AudioConfig.fromStreamInput(audioStream);
        }
        catch (error) {
            console.error('Error creating audio config:', error);
            throw new Error('Failed to create audio configuration');
        }
    }
    // 連續語音識別（用於長時間錄音）
    async continuousSpeechToText(audioBuffer) {
        if (!this.speechConfig) {
            throw new Error(constants_1.ERROR_MESSAGES.API_KEY_MISSING);
        }
        return new Promise((resolve, reject) => {
            try {
                const audioConfig = this.createAudioConfigFromBuffer(audioBuffer);
                const recognizer = new sdk.SpeechRecognizer(this.speechConfig, audioConfig);
                let fullText = '';
                let isCompleted = false;
                recognizer.recognized = (_, e) => {
                    if (e.result.reason === sdk.ResultReason.RecognizedSpeech) {
                        fullText += e.result.text + ' ';
                        console.log(`Recognized: ${e.result.text}`);
                    }
                };
                recognizer.canceled = (_, e) => {
                    console.log(`Recognition canceled: ${e.reason}`);
                    recognizer.stopContinuousRecognitionAsync();
                    if (e.reason === sdk.CancellationReason.Error) {
                        reject(new Error(`Error: ${e.errorCode} - ${e.errorDetails}`));
                    }
                    else if (!isCompleted) {
                        resolve(fullText.trim());
                    }
                };
                recognizer.sessionStopped = (_, _e) => {
                    console.log('Session stopped');
                    recognizer.stopContinuousRecognitionAsync();
                    if (!isCompleted) {
                        isCompleted = true;
                        resolve(fullText.trim());
                    }
                };
                // 開始連續識別
                recognizer.startContinuousRecognitionAsync(() => {
                    console.log('Continuous recognition started');
                }, (error) => {
                    console.error('Failed to start continuous recognition:', error);
                    reject(new Error(`Failed to start recognition: ${error}`));
                });
                // 設置超時
                setTimeout(() => {
                    if (!isCompleted) {
                        isCompleted = true;
                        recognizer.stopContinuousRecognitionAsync();
                        resolve(fullText.trim());
                    }
                }, constants_1.API_CONFIG.timeout);
            }
            catch (error) {
                console.error('Continuous speech recognition error:', error);
                reject(error);
            }
        });
    }
    // 檢查服務可用性
    async checkServiceAvailability() {
        if (!this.speechConfig) {
            return false;
        }
        try {
            // 創建一個簡單的測試識別器
            const audioConfig = sdk.AudioConfig.fromDefaultMicrophoneInput();
            const recognizer = new sdk.SpeechRecognizer(this.speechConfig, audioConfig);
            // 立即關閉以測試連接
            recognizer.close();
            return true;
        }
        catch (error) {
            console.error('Service availability check failed:', error);
            return false;
        }
    }
    // 獲取支持的語言列表
    getSupportedLanguages() {
        return [
            'zh-TW', // 繁體中文（台灣）
            'zh-CN', // 簡體中文（中國）
            'en-US', // 英語（美國）
            'en-GB', // 英語（英國）
            'ja-JP', // 日語
            'ko-KR', // 韓語
            'es-ES', // 西班牙語
            'fr-FR', // 法語
            'de-DE', // 德語
            'it-IT' // 義大利語
        ];
    }
    // 設置識別語言
    setRecognitionLanguage(language) {
        if (this.speechConfig) {
            this.speechConfig.speechRecognitionLanguage = language;
            console.log(`Recognition language set to: ${language}`);
        }
    }
    // 清理資源
    cleanup() {
        if (this.speechConfig) {
            this.speechConfig.close();
            this.speechConfig = null;
        }
        this.removeAllListeners();
    }
}
exports.AzureSpeechService = AzureSpeechService;

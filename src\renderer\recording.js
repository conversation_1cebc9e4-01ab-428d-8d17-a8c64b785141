const { ipc<PERSON><PERSON><PERSON> } = require('electron')

let startTime = Date.now()
let durationInterval = null

// 狀態圖標映射
const statusIcons = {
    recording: '🎤',
    processing: '⚙️',
    completed: '✅',
    error: '❌'
}

// 模式文本映射
const modeTexts = {
    ai: 'AI 智能模式',
    direct: '直接轉錄模式'
}

// 狀態文本映射
const statusTexts = {
    recording: '正在錄音...',
    processing: '處理中...',
    completed: '完成',
    error: '錯誤'
}

// 監聽狀態更新
ipcRenderer.on('status-update', (event, status) => {
    updateUI(status)
})

function updateUI(status) {
    const icon = document.getElementById('recordingIcon')
    const modeText = document.getElementById('modeText')
    const statusText = document.getElementById('statusText')
    const message = document.getElementById('message')

    // 更新圖標
    icon.textContent = statusIcons[status.status] || '🎤'
    icon.className = `recording-icon ${status.status}`

    // 更新模式文本
    modeText.textContent = modeTexts[status.mode] || 'AI 智能模式'

    // 更新狀態文本
    statusText.textContent = statusTexts[status.status] || '正在錄音...'

    // 更新消息
    if (status.message) {
        message.textContent = status.message
        message.style.display = 'block'
    } else {
        message.style.display = 'none'
    }

    // 更新音量檢測
    if (typeof status.volume === 'number') {
        const volumeFill = document.getElementById('volumeFill')
        const volumeText = document.getElementById('volumeText')
        const volume = Math.max(0, Math.min(100, status.volume))
        
        volumeFill.style.width = volume + '%'
        volumeText.textContent = volume + '%'
    }

    // 管理計時器 - 直接模式下即使在處理中也要保持計時器運行
    if (status.isRecording && (status.status === 'recording' || status.status === 'processing')) {
        startDurationTimer()
    } else {
        stopDurationTimer()
    }
}

function startDurationTimer() {
    if (durationInterval) return

    startTime = Date.now()
    durationInterval = setInterval(() => {
        const elapsed = Math.floor((Date.now() - startTime) / 1000)
        const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0')
        const seconds = (elapsed % 60).toString().padStart(2, '0')
        document.getElementById('duration').textContent = `${minutes}:${seconds}`
    }, 1000)
}

function stopDurationTimer() {
    if (durationInterval) {
        clearInterval(durationInterval)
        durationInterval = null
    }
}

function closeWindow() {
    window.close()
}

// 窗口拖拽功能
let isDragging = false
let dragOffset = { x: 0, y: 0 }

document.addEventListener('mousedown', (e) => {
    if (e.target.classList.contains('close-btn')) return
    
    isDragging = true
    dragOffset.x = e.clientX
    dragOffset.y = e.clientY
})

document.addEventListener('mousemove', (e) => {
    if (!isDragging) return
    
    const deltaX = e.clientX - dragOffset.x
    const deltaY = e.clientY - dragOffset.y
    
    ipcRenderer.send('move-window', { deltaX, deltaY })
    
    dragOffset.x = e.clientX
    dragOffset.y = e.clientY
})

document.addEventListener('mouseup', () => {
    isDragging = false
})

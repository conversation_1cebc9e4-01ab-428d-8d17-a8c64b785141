{"extends": "./tsconfig.json", "compilerOptions": {"noEmit": false, "outDir": "dist", "module": "CommonJS", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowImportingTsExtensions": false, "baseUrl": ".", "paths": {"@shared/*": ["src/shared/*"]}}, "include": ["src/main/**/*.ts", "src/preload/**/*.ts", "src/shared/**/*.ts"], "exclude": ["src/renderer/**/*"]}
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.WindowManager = void 0;
const electron_1 = require("electron");
const path_1 = require("path");
const constants_1 = require("../../shared/constants");
const http = __importStar(require("http"));
class WindowManager {
    constructor() {
        Object.defineProperty(this, "mainWindow", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: null
        });
        Object.defineProperty(this, "settingsWindow", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: null
        });
        Object.defineProperty(this, "recordingWindow", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: null
        });
        Object.defineProperty(this, "devUrl", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: null
        });
    }
    async getDevUrl() {
        if (this.devUrl) {
            return this.devUrl;
        }
        const ports = [5173, 5174, 3000];
        for (const port of ports) {
            try {
                await this.checkPort(port);
                this.devUrl = `http://localhost:${port}`;
                return this.devUrl;
            }
            catch (error) {
                // 繼續嘗試下一個端口
            }
        }
        throw new Error('No development server found');
    }
    checkPort(port) {
        return new Promise((resolve, reject) => {
            const req = http.request({
                hostname: 'localhost',
                port: port,
                method: 'GET',
                timeout: 1000
            }, (_res) => {
                resolve();
            });
            req.on('error', reject);
            req.on('timeout', () => reject(new Error('Timeout')));
            req.end();
        });
    }
    async createMainWindow() {
        if (this.mainWindow) {
            return this.mainWindow;
        }
        const { width, height, minWidth, minHeight } = constants_1.WINDOW_CONFIG.main;
        this.mainWindow = new electron_1.BrowserWindow({
            width,
            height,
            minWidth,
            minHeight,
            show: false,
            frame: true,
            titleBarStyle: 'default',
            icon: (0, path_1.join)(__dirname, '../../../assets/logo5.png'),
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: process.env.NODE_ENV === 'development'
                    ? (0, path_1.join)(__dirname, '../../preload/preload.js')
                    : (0, path_1.join)(__dirname, '../../preload/preload.js')
            }
        });
        // 載入應用程式
        if (process.env.NODE_ENV === 'development') {
            try {
                // 等待開發服務器啟動
                await new Promise(resolve => setTimeout(resolve, 2000));
                // 嘗試多個端口
                const ports = [5174, 5173, 3000];
                let loaded = false;
                for (const port of ports) {
                    if (this.mainWindow.isDestroyed()) {
                        console.log('Window was destroyed, aborting load');
                        return this.mainWindow;
                    }
                    try {
                        console.log(`Trying to load development URL: http://localhost:${port}`);
                        await this.mainWindow.loadURL(`http://localhost:${port}`);
                        console.log(`Development URL loaded successfully on port ${port}`);
                        loaded = true;
                        break;
                    }
                    catch (portError) {
                        console.log(`Port ${port} failed, trying next...`);
                    }
                }
                if (!loaded) {
                    throw new Error('All development ports failed');
                }
                if (!this.mainWindow.isDestroyed()) {
                    this.mainWindow.webContents.openDevTools();
                }
            }
            catch (error) {
                console.error('Failed to load development URL:', error);
                // 如果開發服務器連接失敗，嘗試載入構建的文件
                console.log('Falling back to built files...');
                if (!this.mainWindow.isDestroyed()) {
                    const htmlPath = (0, path_1.join)(__dirname, '../../../dist-renderer/index.html');
                    console.log('Loading HTML from:', htmlPath);
                    console.log('__dirname:', __dirname);
                    await this.mainWindow.loadFile(htmlPath);
                }
            }
        }
        else {
            if (!this.mainWindow.isDestroyed()) {
                const htmlPath = (0, path_1.join)(__dirname, '../../../dist-renderer/index.html');
                console.log('Production mode - Loading HTML from:', htmlPath);
                console.log('__dirname:', __dirname);
                await this.mainWindow.loadFile(htmlPath);
            }
        }
        // 窗口事件處理
        this.mainWindow.on('ready-to-show', () => {
            this.mainWindow?.show();
            this.mainWindow?.focus();
        });
        this.mainWindow.on('close', (event) => {
            // 防止窗口關閉，改為隱藏到系統托盤
            event.preventDefault();
            this.hideMainWindow();
        });
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });
        return this.mainWindow;
    }
    async createSettingsWindow() {
        if (this.settingsWindow) {
            return this.settingsWindow;
        }
        const { width, height, minWidth, minHeight } = constants_1.WINDOW_CONFIG.settings;
        this.settingsWindow = new electron_1.BrowserWindow({
            width,
            height,
            minWidth,
            minHeight,
            show: false,
            modal: true,
            parent: this.mainWindow || undefined,
            frame: true,
            titleBarStyle: 'default',
            icon: (0, path_1.join)(__dirname, '../../../assets/logo5.png'),
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: process.env.NODE_ENV === 'development'
                    ? (0, path_1.join)(__dirname, '../../preload/preload.js')
                    : (0, path_1.join)(__dirname, '../../preload/preload.js')
            }
        });
        // 載入設定頁面
        if (process.env.NODE_ENV === 'development') {
            const devUrl = await this.getDevUrl();
            await this.settingsWindow.loadURL(devUrl);
        }
        else {
            await this.settingsWindow.loadFile((0, path_1.join)(__dirname, '../../../dist-renderer/index.html'));
        }
        this.settingsWindow.on('closed', () => {
            this.settingsWindow = null;
        });
        return this.settingsWindow;
    }
    async createRecordingWindow() {
        if (this.recordingWindow) {
            return this.recordingWindow;
        }
        const { width, height } = constants_1.WINDOW_CONFIG.recording;
        const primaryDisplay = electron_1.screen.getPrimaryDisplay();
        const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;
        // 將錄音視窗置於螢幕中央
        const x = Math.round((screenWidth - width) / 2);
        const y = Math.round((screenHeight - height) / 2);
        this.recordingWindow = new electron_1.BrowserWindow({
            width,
            height,
            x,
            y,
            show: false,
            frame: true,
            alwaysOnTop: true,
            skipTaskbar: true,
            resizable: false,
            minimizable: false,
            maximizable: false,
            titleBarStyle: 'default',
            icon: (0, path_1.join)(__dirname, '../../../assets/logo5.png'),
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: process.env.NODE_ENV === 'development'
                    ? (0, path_1.join)(__dirname, '../../preload/preload.js')
                    : (0, path_1.join)(__dirname, '../../preload/preload.js')
            }
        });
        // 載入錄音頁面
        if (process.env.NODE_ENV === 'development') {
            const devUrl = await this.getDevUrl();
            await this.recordingWindow.loadURL(devUrl);
        }
        else {
            await this.recordingWindow.loadFile((0, path_1.join)(__dirname, '../../../dist-renderer/index.html'));
        }
        this.recordingWindow.on('closed', () => {
            this.recordingWindow = null;
        });
        return this.recordingWindow;
    }
    // 主窗口控制
    showMainWindow() {
        if (!this.mainWindow) {
            this.createMainWindow();
            return;
        }
        if (this.mainWindow.isMinimized()) {
            this.mainWindow.restore();
        }
        this.mainWindow.show();
        this.mainWindow.focus();
    }
    hideMainWindow() {
        this.mainWindow?.hide();
    }
    // 設定窗口控制
    async showSettingsWindow() {
        if (!this.settingsWindow) {
            await this.createSettingsWindow();
        }
        this.settingsWindow?.show();
        this.settingsWindow?.focus();
    }
    hideSettingsWindow() {
        this.settingsWindow?.hide();
    }
    // 錄音窗口控制
    async showRecordingWindow() {
        if (!this.recordingWindow) {
            await this.createRecordingWindow();
        }
        this.recordingWindow?.show();
        this.recordingWindow?.focus();
    }
    hideRecordingWindow() {
        this.recordingWindow?.hide();
    }
    // 廣播消息到所有窗口
    broadcast(channel, data) {
        const windows = [this.mainWindow, this.settingsWindow, this.recordingWindow];
        windows.forEach(window => {
            if (window && !window.isDestroyed()) {
                window.webContents.send(channel, data);
            }
        });
    }
    // 發送消息到特定窗口
    sendToWindow(windowType, channel, data) {
        let window = null;
        switch (windowType) {
            case 'main':
                window = this.mainWindow;
                break;
            case 'settings':
                window = this.settingsWindow;
                break;
            case 'recording':
                window = this.recordingWindow;
                break;
        }
        if (window && !window.isDestroyed()) {
            window.webContents.send(channel, data);
        }
    }
    // 獲取窗口實例
    getWindow(windowType) {
        switch (windowType) {
            case 'main':
                return this.mainWindow;
            case 'settings':
                return this.settingsWindow;
            case 'recording':
                return this.recordingWindow;
            default:
                return null;
        }
    }
    // 關閉所有窗口
    closeAllWindows() {
        [this.mainWindow, this.settingsWindow, this.recordingWindow].forEach(window => {
            if (window && !window.isDestroyed()) {
                window.destroy();
            }
        });
        this.mainWindow = null;
        this.settingsWindow = null;
        this.recordingWindow = null;
    }
    // 檢查窗口是否存在且可見
    isWindowVisible(windowType) {
        const window = this.getWindow(windowType);
        return window ? window.isVisible() : false;
    }
}
exports.WindowManager = WindowManager;

# SpeechPilot - AI語音助手

SpeechPilot是一個桌面應用程式，讓你可以在任何應用程式中使用語音輸入和AI指令處理。只需按下快捷鍵，說出你想要的內容，AI就會理解並自動輸入到當前的文字欄位中。 Speech in, text out.
暫時提供兩個模式:
1. AI Speech-to-Text, 用戶說話, AI理解後根據指示提供回應並自動輸入到當前的文字欄位中
- 例如: 用戶說: 幫我寫一篇100字的LINKEDIN POST關於得獎感受。AI會理解這是寫作任務，並自動生成文章並輸入到當前的文字欄位中
- gpt-4o-mini-audio-preview模型能直接進行整個流程
- 後續可能會加入其他MODEL

2. Direct Speech-to-Text, 用戶說話, AI直接把文字輸入到當前的文字欄位中
- 例如: 用戶說: 我的名字是John。AI會直接輸入 "我的名字是John" 到當前的文字欄位中
- azure speech service就足以進行整個流程

## 功能特色

- 🎤 **全域語音錄製**: 在任何應用程式中按下快捷鍵即可開始錄音，使用 Web Audio API 進行高品質音頻捕獲
- 🤖 **AI指令理解**: 整合 Azure OpenAI gpt-4o-mini-audio-preview 模型，不只是語音轉文字，還能理解複雜指令如"寫一篇100字的文章"
- 📝 **精確語音識別**: 使用 Azure Speech Service 提供高精度的語音轉文字功能
- ⌨️ **智能文字輸入**: 使用 nut.js 實現真正的自動文字輸入，支援剪貼簿備用模式
- 🔧 **完整配置管理**: 在設定面板中安全配置 API 金鑰和自定義快捷鍵
- 🚨 **智能錯誤處理**: 完整的錯誤處理機制和用戶友好的通知系統
- 🌐 **跨平台支援**: 支援Windows和macOS
- ⚡ **現代化架構**: 使用 Electron + Vite + React + TypeScript 提供快速開發體驗
- 🎨 **美觀介面**: React 組件化設計，支援 Framer Motion 動畫和互動效果

## 使用說明

### 快捷鍵操作
- **Ctrl+Shift+C**: 以AI SPEECH TO TEXT模式馬上開始錄音, 彈出開始錄音視窗, 並開始錄製音頻, AI會理解你的語音內容並根據需求生成回應後輸入到當前的文字欄位中, 再按一次快捷鍵或點擊停止錄音按鈕停止錄音
- **Ctrl+Shift+V**: 以DIRECT SPEECH TO TEXT模式馬上開始錄音, 彈出開始錄音視窗, 並開始錄製音頻,並直接把文字輸入到當前的文字欄位中, 再按一次快捷鍵或點擊停止錄音按鈕停止錄音

### 設定面板
1. 點擊主介面右上角的設定按鈕
2. 可以自定義快捷鍵組合



3. 配置 Azure API 金鑰
4. 調整其他偏好設定
5. 可調整HOTKEY按著或是按一下開始錄音
6. 可選擇用哪個錄音裝置

### 開始錄音視窗
- 錄音視窗會顯示錄音狀態和進度
- 錄音過程中，再次按下快捷鍵或點擊停止錄音按鈕即可停止錄音
- 錄音完成後，會顯示處理狀態和結果
- 錄音完成後，會自動關閉錄音視窗

## 技術架構

### 項目結構
```
src/
├── main/
│   ├── main.ts              # 主進程入口
│   ├── services/            # 服務層
│   │   ├── AudioService.ts  # 音頻錄製服務
│   │   ├── AIService.ts     # AI 處理服務
│   │   └── TextInputService.ts # 文字輸入服務
│   ├── windows/             # 窗口管理
│   │   └── RecordingWindow.ts # 錄音窗口
│   └── renderer/            # 渲染器資源
│       └── recording.html   # 錄音窗口 HTML
```

### 重構亮點
- ✅ **模組化架構**: 將原本 492 行的單一文件重構為多個專門的服務模組
- ✅ **服務分離**: AudioService、AIService、TextInputService 各司其職
- ✅ **窗口管理**: RecordingWindow 專門處理錄音界面
- ✅ **代碼復用**: 服務可以在不同場景下重複使用
- ✅ **易於維護**: 每個模組職責單一，便於測試和維護

### HIGH LEVEL DESIGN
- Standard Best Practice TSX VITE REACT FRAMEWORK
- I expect to have a tidy and well-structured code-base

### SETTING MENU
- AUDIO DEVICE SELECTION
- START APP WHEN OS START
- HOTKEY MODE (TOGGLE OR HOLD)
- SHORTCUT KEYS
- MULTI-SELECTION FOR USER SELECT USUAL LANGUAGE THEY SPEAK
- SET TIMEOUT SECOND WHEN RECORDING STOP
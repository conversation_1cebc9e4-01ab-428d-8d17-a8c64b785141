"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AudioService = void 0;
const child_process_1 = require("child_process");
const os_1 = require("os");
const ffmpeg_static_1 = __importDefault(require("ffmpeg-static"));
class AudioService {
    constructor() {
        this.selectedAudioDevice = 'Microphone (Logi C270 HD WebCam)';
    }
    async detectWindowsAudioDevices() {
        return new Promise((resolve) => {
            console.log('🔍 Detecting Windows audio devices...');
            const detectProcess = (0, child_process_1.spawn)(ffmpeg_static_1.default, [
                '-list_devices', 'true',
                '-f', 'dshow',
                '-i', 'dummy'
            ]);
            let output = '';
            detectProcess.stderr?.on('data', (data) => {
                output += data.toString();
            });
            detectProcess.on('close', () => {
                // 解析音頻設備
                const audioDevices = [];
                const lines = output.split('\n');
                for (const line of lines) {
                    if (line.includes('(audio)')) {
                        const match = line.match(/"([^"]+)"\s*\(audio\)/);
                        if (match) {
                            audioDevices.push(match[1]);
                        }
                    }
                }
                console.log('🎤 Found audio devices:', audioDevices);
                resolve(audioDevices);
            });
            detectProcess.on('error', (error) => {
                console.error('❌ Failed to detect audio devices:', error);
                resolve([]);
            });
        });
    }
    async getSelectedAudioDevice() {
        if ((0, os_1.platform)() === 'win32') {
            const availableDevices = await this.detectWindowsAudioDevices();
            // 檢查選定的設備是否可用
            if (availableDevices.includes(this.selectedAudioDevice)) {
                return this.selectedAudioDevice;
            }
            // 如果選定設備不可用，使用第一個可用設備
            console.warn('⚠️ Selected device not found, using first available device');
            return availableDevices[0] || '';
        }
        return '';
    }
    setSelectedAudioDevice(deviceName) {
        this.selectedAudioDevice = deviceName;
        console.log('🎤 Audio device changed to:', deviceName);
    }
    createRecordingProcess(audioDevice) {
        if (!ffmpeg_static_1.default) {
            throw new Error('FFmpeg binary not found');
        }
        console.log('📁 FFmpeg path:', ffmpeg_static_1.default);
        console.log('🎤 Using audio device:', audioDevice);
        if ((0, os_1.platform)() === 'darwin') {
            // macOS - OpenAI Realtime API 需要 24kHz
            return (0, child_process_1.spawn)(ffmpeg_static_1.default, [
                '-f', 'avfoundation',
                '-i', ':0',
                '-ar', '24000', // 24kHz for OpenAI Realtime API
                '-ac', '1', // 單聲道
                '-f', 'wav',
                '-'
            ]);
        }
        else {
            // Windows - OpenAI Realtime API 需要 24kHz
            return (0, child_process_1.spawn)(ffmpeg_static_1.default, [
                '-f', 'dshow',
                '-i', `audio=${audioDevice}`,
                '-ar', '24000', // 24kHz for OpenAI Realtime API
                '-ac', '1', // 單聲道
                '-f', 'wav',
                '-'
            ]);
        }
    }
}
exports.AudioService = AudioService;

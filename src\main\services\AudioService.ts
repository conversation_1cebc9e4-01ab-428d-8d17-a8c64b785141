import { spawn } from 'child_process'
import { platform } from 'os'
import ffmpegPath from 'ffmpeg-static'

export interface AudioDevice {
  name: string
  isDefault?: boolean
}

export class AudioService {
  private selectedAudioDevice = 'Microphone (Logi C270 HD WebCam)'

  async detectWindowsAudioDevices(): Promise<string[]> {
    return new Promise((resolve) => {
      console.log('🔍 Detecting Windows audio devices...')
      
      const detectProcess = spawn(ffmpegPath!, [
        '-list_devices', 'true',
        '-f', 'dshow',
        '-i', 'dummy'
      ])

      let output = ''
      
      detectProcess.stderr?.on('data', (data: Buffer) => {
        output += data.toString()
      })

      detectProcess.on('close', () => {
        // 解析音頻設備
        const audioDevices = []
        const lines = output.split('\n')
        
        for (const line of lines) {
          if (line.includes('(audio)')) {
            const match = line.match(/"([^"]+)"\s*\(audio\)/)
            if (match) {
              audioDevices.push(match[1])
            }
          }
        }

        console.log('🎤 Found audio devices:', audioDevices)
        resolve(audioDevices)
      })

      detectProcess.on('error', (error) => {
        console.error('❌ Failed to detect audio devices:', error)
        resolve([])
      })
    })
  }

  async getSelectedAudioDevice(): Promise<string> {
    if (platform() === 'win32') {
      const availableDevices = await this.detectWindowsAudioDevices()
      
      // 檢查選定的設備是否可用
      if (availableDevices.includes(this.selectedAudioDevice)) {
        return this.selectedAudioDevice
      }
      
      // 如果選定設備不可用，使用第一個可用設備
      console.warn('⚠️ Selected device not found, using first available device')
      return availableDevices[0] || ''
    }
    
    return ''
  }

  setSelectedAudioDevice(deviceName: string) {
    this.selectedAudioDevice = deviceName
    console.log('🎤 Audio device changed to:', deviceName)
  }

  createRecordingProcess(audioDevice: string): any {
    if (!ffmpegPath) {
      throw new Error('FFmpeg binary not found')
    }

    console.log('📁 FFmpeg path:', ffmpegPath)
    console.log('🎤 Using audio device:', audioDevice)

    if (platform() === 'darwin') {
      // macOS - OpenAI Realtime API 需要 24kHz
      return spawn(ffmpegPath, [
        '-f', 'avfoundation',
        '-i', ':0',
        '-ar', '24000',  // 24kHz for OpenAI Realtime API
        '-ac', '1',      // 單聲道
        '-f', 'wav',
        '-'
      ])
    } else {
      // Windows - OpenAI Realtime API 需要 24kHz
      return spawn(ffmpegPath, [
        '-f', 'dshow',
        '-i', `audio=${audioDevice}`,
        '-ar', '24000',  // 24kHz for OpenAI Realtime API
        '-ac', '1',      // 單聲道
        '-f', 'wav',
        '-'
      ])
    }
  }
}

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SettingsWindow = void 0;
const electron_1 = require("electron");
class SettingsWindow {
    constructor() {
        this.window = null;
        this.store = null;
        this.initStore();
    }
    async initStore() {
        try {
            // 使用簡單的 JSON 文件存儲，避免 ESM 問題
            const fs = require('fs');
            const path = require('path');
            const { app } = require('electron');
            const settingsPath = path.join(app.getPath('userData'), 'settings.json');
            // 默認設置
            const defaultSettings = {
                audioDevice: 'default',
                hotkeys: {
                    ai: 'CommandOrControl+Shift+C',
                    direct: 'CommandOrControl+Shift+V'
                },
                volume: {
                    threshold: 10
                }
            };
            // 創建簡單的 store 對象
            this.store = {
                get: (key) => {
                    try {
                        if (fs.existsSync(settingsPath)) {
                            const data = JSON.parse(fs.readFileSync(settingsPath, 'utf8'));
                            return data[key] || defaultSettings[key];
                        }
                        return defaultSettings[key];
                    }
                    catch {
                        return defaultSettings[key];
                    }
                },
                set: (key, value) => {
                    try {
                        let data = {};
                        if (fs.existsSync(settingsPath)) {
                            data = JSON.parse(fs.readFileSync(settingsPath, 'utf8'));
                        }
                        data[key] = value;
                        fs.writeFileSync(settingsPath, JSON.stringify(data, null, 2));
                    }
                    catch (error) {
                        console.error('❌ Failed to save setting:', error);
                    }
                }
            };
            console.log('⚙️ Simple JSON store initialized successfully');
        }
        catch (error) {
            console.error('❌ Failed to initialize store:', error);
        }
    }
    show() {
        if (this.window) {
            this.window.focus();
            return;
        }
        const { width, height } = electron_1.screen.getPrimaryDisplay().workAreaSize;
        this.window = new electron_1.BrowserWindow({
            width: 600,
            height: 500,
            x: Math.round((width - 600) / 2),
            y: Math.round((height - 500) / 2),
            resizable: true,
            minimizable: true,
            maximizable: true,
            alwaysOnTop: false,
            skipTaskbar: false,
            frame: true,
            title: 'SpeechPilot 設置',
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: false
            }
        });
        // 載入 HTML 內容
        console.log('🪟 Loading settings HTML content...');
        this.window.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(this.getSettingsHTML())}`)
            .then(() => {
            console.log('🪟 Settings HTML loaded successfully');
        })
            .catch((error) => {
            console.error('❌ Failed to load settings HTML:', error);
        });
        this.window.on('closed', () => {
            this.window = null;
        });
        // 設置 IPC 處理
        this.setupIPC();
    }
    setupIPC() {
        if (!this.store) {
            console.error('❌ Store not initialized, cannot setup IPC');
            return;
        }
        // 移除現有的處理程序（如果存在）
        electron_1.ipcMain.removeHandler('get-settings');
        electron_1.ipcMain.removeHandler('save-settings');
        // 獲取設置
        electron_1.ipcMain.handle('get-settings', () => {
            if (!this.store)
                return null;
            return {
                audioDevice: this.store.get('audioDevice'),
                hotkeys: this.store.get('hotkeys'),
                volume: this.store.get('volume')
            };
        });
        // 保存設置
        electron_1.ipcMain.handle('save-settings', (_event, settings) => {
            if (!this.store)
                return false;
            Object.keys(settings).forEach(key => {
                this.store.set(key, settings[key]);
            });
            console.log('💾 Settings saved:', settings);
            return true;
        });
        // 獲取音頻設備列表
        electron_1.ipcMain.handle('get-audio-devices', async () => {
            try {
                const { AudioService } = require('../services/AudioService');
                const audioService = new AudioService();
                const devices = await audioService.getAudioDevices();
                return devices;
            }
            catch (error) {
                console.error('❌ Failed to get audio devices:', error);
                return [];
            }
        });
    }
    // 獲取設置數據
    getSettings() {
        return {
            audioDevice: this.store.get('audioDevice'),
            hotkeys: this.store.get('hotkeys'),
            volume: this.store.get('volume')
        };
    }
    // 保存設置數據
    saveSettings(settings) {
        Object.keys(settings).forEach(key => {
            this.store.set(key, settings[key]);
        });
    }
    getSettingsHTML() {
        return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>SpeechPilot 設置</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      min-height: 100vh;
      box-sizing: border-box;
    }

    .container {
      max-width: 500px;
      margin: 0 auto;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      padding: 30px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    h1 {
      text-align: center;
      margin-bottom: 30px;
      font-size: 28px;
      font-weight: 300;
    }

    .setting-group {
      margin-bottom: 25px;
      padding: 20px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 15px;
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .setting-label {
      display: block;
      margin-bottom: 10px;
      font-weight: 500;
      font-size: 16px;
    }

    select, input {
      width: 100%;
      padding: 12px;
      border: none;
      border-radius: 10px;
      background: rgba(255, 255, 255, 0.9);
      color: #333;
      font-size: 14px;
      box-sizing: border-box;
    }

    select:focus, input:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
    }

    .hotkey-group {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;
    }

    .save-button {
      width: 100%;
      padding: 15px;
      background: linear-gradient(45deg, #4CAF50, #45a049);
      color: white;
      border: none;
      border-radius: 10px;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-top: 20px;
    }

    .save-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
    }

    .loading {
      text-align: center;
      padding: 20px;
      opacity: 0.7;
    }

    .status {
      text-align: center;
      padding: 10px;
      margin-top: 10px;
      border-radius: 10px;
      font-weight: 500;
    }

    .status.success {
      background: rgba(76, 175, 80, 0.3);
      color: #4CAF50;
    }

    .status.error {
      background: rgba(244, 67, 54, 0.3);
      color: #f44336;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🎙️ SpeechPilot 設置</h1>
    
    <div class="setting-group">
      <label class="setting-label">音頻輸入設備</label>
      <select id="audioDevice">
        <option value="">載入中...</option>
      </select>
    </div>

    <div class="setting-group">
      <label class="setting-label">快捷鍵設置</label>
      <div class="hotkey-group">
        <div>
          <label class="setting-label">AI 模式</label>
          <input type="text" id="aiHotkey" placeholder="Ctrl+Shift+A" readonly>
        </div>
        <div>
          <label class="setting-label">直接轉錄</label>
          <input type="text" id="directHotkey" placeholder="Ctrl+Shift+V" readonly>
        </div>
      </div>
    </div>

    <div class="setting-group">
      <label class="setting-label">音量檢測閾值 (0-100)</label>
      <input type="range" id="volumeThreshold" min="0" max="100" value="10">
      <div style="text-align: center; margin-top: 5px;">
        <span id="volumeValue">10</span>
      </div>
    </div>

    <button class="save-button" onclick="saveSettings()">💾 保存設置</button>
    
    <div id="status" class="status" style="display: none;"></div>
  </div>

  <script>
    const { ipcRenderer } = require('electron')
    
    let currentSettings = {}

    // 載入設置
    async function loadSettings() {
      try {
        currentSettings = await ipcRenderer.invoke('get-settings')
        
        // 載入音頻設備
        const devices = await ipcRenderer.invoke('get-audio-devices')
        const deviceSelect = document.getElementById('audioDevice')
        deviceSelect.innerHTML = ''
        
        // 添加默認選項
        const defaultOption = document.createElement('option')
        defaultOption.value = 'default'
        defaultOption.textContent = '系統默認設備'
        deviceSelect.appendChild(defaultOption)
        
        // 添加設備選項
        devices.forEach(device => {
          const option = document.createElement('option')
          option.value = device.name
          option.textContent = device.name
          deviceSelect.appendChild(option)
        })
        
        // 設置當前值
        deviceSelect.value = currentSettings.audioDevice || 'default'
        document.getElementById('aiHotkey').value = currentSettings.hotkeys?.ai || 'CommandOrControl+Shift+A'
        document.getElementById('directHotkey').value = currentSettings.hotkeys?.direct || 'CommandOrControl+Shift+V'
        document.getElementById('volumeThreshold').value = currentSettings.volume?.threshold || 10
        document.getElementById('volumeValue').textContent = currentSettings.volume?.threshold || 10
        
      } catch (error) {
        console.error('Failed to load settings:', error)
        showStatus('載入設置失敗', 'error')
      }
    }

    // 保存設置
    async function saveSettings() {
      try {
        const settings = {
          audioDevice: document.getElementById('audioDevice').value,
          hotkeys: {
            ai: document.getElementById('aiHotkey').value,
            direct: document.getElementById('directHotkey').value
          },
          volume: {
            threshold: parseInt(document.getElementById('volumeThreshold').value)
          }
        }
        
        await ipcRenderer.invoke('save-settings', settings)
        showStatus('設置已保存', 'success')
        
      } catch (error) {
        console.error('Failed to save settings:', error)
        showStatus('保存設置失敗', 'error')
      }
    }

    // 顯示狀態消息
    function showStatus(message, type) {
      const status = document.getElementById('status')
      status.textContent = message
      status.className = 'status ' + type
      status.style.display = 'block'
      
      setTimeout(() => {
        status.style.display = 'none'
      }, 3000)
    }

    // 音量滑塊事件
    document.getElementById('volumeThreshold').addEventListener('input', function() {
      document.getElementById('volumeValue').textContent = this.value
    })

    // 載入設置
    loadSettings()
  </script>
</body>
</html>
    `;
    }
}
exports.SettingsWindow = SettingsWindow;

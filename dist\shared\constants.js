"use strict";
// 應用程式常數
Object.defineProperty(exports, "__esModule", { value: true });
exports.ERROR_MESSAGES = exports.API_CONFIG = exports.AUDIO_CONFIG = exports.WINDOW_CONFIG = exports.DEFAULT_CONFIG = exports.APP_VERSION = exports.APP_NAME = void 0;
exports.APP_NAME = 'SpeechPilot';
exports.APP_VERSION = '1.0.0';
// 預設配置
exports.DEFAULT_CONFIG = {
    azureOpenAI: {
        endpoint: '',
        apiKey: '',
        model: 'gpt-4o-mini-audio-preview',
        transcribeModel: 'gpt-4o-mini-transcribe'
    },
    azureSpeech: {
        region: 'eastus',
        apiKey: ''
    },
    hotkeys: {
        aiMode: 'CommandOrControl+Shift+C',
        directMode: 'CommandOrControl+Shift+V'
    },
    recording: {
        deviceId: 'default',
        hotkeyMode: 'toggle'
    },
    ui: {
        theme: 'light',
        language: 'zh-TW'
    }
};
// 窗口配置
exports.WINDOW_CONFIG = {
    main: {
        width: 800, // 修改為 800x600
        height: 600, // 修改為 800x600
        minWidth: 700, // 最小寬度調整
        minHeight: 500, // 最小高度調整
        resizable: true,
        show: false,
        frame: true,
        titleBarStyle: 'default'
    },
    settings: {
        width: 900, // 增加 50%: 600 -> 900
        height: 750, // 增加 50%: 500 -> 750
        minWidth: 750, // 增加 50%: 500 -> 750
        minHeight: 600, // 增加 50%: 400 -> 600
        resizable: true,
        show: false,
        modal: true,
        frame: true
    },
    recording: {
        width: 750, // 增加 50%: 500 -> 750
        height: 450, // 增加 50%: 300 -> 450
        minWidth: 600, // 增加 50%: 400 -> 600
        minHeight: 375, // 增加 50%: 250 -> 375
        resizable: true,
        show: false,
        frame: true,
        alwaysOnTop: true,
        skipTaskbar: true
    }
};
// 音頻配置
exports.AUDIO_CONFIG = {
    sampleRate: 16000,
    channels: 1,
    bitsPerSample: 16,
    format: 'wav',
    maxDuration: 300, // 5 分鐘
    minDuration: 0.5 // 0.5 秒
};
// API 配置
exports.API_CONFIG = {
    timeout: 30000, // 30 秒
    retries: 3,
    retryDelay: 1000 // 1 秒
};
// 錯誤訊息
exports.ERROR_MESSAGES = {
    NO_MICROPHONE: '找不到麥克風設備',
    PERMISSION_DENIED: '麥克風權限被拒絕',
    RECORDING_FAILED: '錄音失敗',
    PROCESSING_FAILED: '處理失敗',
    API_KEY_MISSING: 'API 金鑰未設定',
    NETWORK_ERROR: '網路連接錯誤',
    INVALID_CONFIG: '配置無效',
    HOTKEY_REGISTER_FAILED: '快捷鍵註冊失敗'
};
